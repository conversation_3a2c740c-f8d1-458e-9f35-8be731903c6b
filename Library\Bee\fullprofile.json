{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 33900, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 33900, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 33900, "tid": 71, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 33900, "tid": 71, "ts": 1754039282727325, "dur": 1685, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282733182, "dur": 2001, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 33900, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282382688, "dur": 16773, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282399464, "dur": 318213, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282399487, "dur": 56, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282399562, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282399569, "dur": 42385, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282441971, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282441980, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282442117, "dur": 16, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282442138, "dur": 2866, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445027, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445039, "dur": 235, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445291, "dur": 15, "ph": "X", "name": "ProcessMessages 4540", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445309, "dur": 96, "ph": "X", "name": "ReadAsync 4540", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445418, "dur": 6, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445428, "dur": 97, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445537, "dur": 7, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445547, "dur": 82, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445636, "dur": 6, "ph": "X", "name": "ProcessMessages 1686", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445644, "dur": 73, "ph": "X", "name": "ReadAsync 1686", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445725, "dur": 4, "ph": "X", "name": "ProcessMessages 1359", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445731, "dur": 81, "ph": "X", "name": "ReadAsync 1359", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445818, "dur": 3, "ph": "X", "name": "ProcessMessages 1290", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445825, "dur": 47, "ph": "X", "name": "ReadAsync 1290", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445878, "dur": 3, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445884, "dur": 60, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445951, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282445956, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446060, "dur": 11, "ph": "X", "name": "ProcessMessages 1626", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446075, "dur": 127, "ph": "X", "name": "ReadAsync 1626", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446211, "dur": 7, "ph": "X", "name": "ProcessMessages 2046", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446221, "dur": 82, "ph": "X", "name": "ReadAsync 2046", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446312, "dur": 6, "ph": "X", "name": "ProcessMessages 2013", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446323, "dur": 96, "ph": "X", "name": "ReadAsync 2013", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446427, "dur": 4, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446434, "dur": 96, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446536, "dur": 6, "ph": "X", "name": "ProcessMessages 2529", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446544, "dur": 117, "ph": "X", "name": "ReadAsync 2529", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446671, "dur": 6, "ph": "X", "name": "ProcessMessages 2460", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446680, "dur": 77, "ph": "X", "name": "ReadAsync 2460", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446764, "dur": 4, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446770, "dur": 94, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446870, "dur": 5, "ph": "X", "name": "ProcessMessages 1850", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446878, "dur": 99, "ph": "X", "name": "ReadAsync 1850", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446985, "dur": 5, "ph": "X", "name": "ProcessMessages 1875", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282446992, "dur": 40, "ph": "X", "name": "ReadAsync 1875", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447035, "dur": 3, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447039, "dur": 59, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447104, "dur": 4, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447111, "dur": 38, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447151, "dur": 3, "ph": "X", "name": "ProcessMessages 1746", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447156, "dur": 50, "ph": "X", "name": "ReadAsync 1746", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447213, "dur": 4, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447220, "dur": 49, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447272, "dur": 3, "ph": "X", "name": "ProcessMessages 1710", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447278, "dur": 43, "ph": "X", "name": "ReadAsync 1710", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447328, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447333, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447377, "dur": 3, "ph": "X", "name": "ProcessMessages 1330", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447382, "dur": 46, "ph": "X", "name": "ReadAsync 1330", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447435, "dur": 3, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447441, "dur": 66, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447513, "dur": 3, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447519, "dur": 47, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447569, "dur": 4, "ph": "X", "name": "ProcessMessages 1703", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447575, "dur": 59, "ph": "X", "name": "ReadAsync 1703", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447640, "dur": 3, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447646, "dur": 49, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447698, "dur": 3, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447703, "dur": 55, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447765, "dur": 3, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447771, "dur": 64, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447841, "dur": 4, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447848, "dur": 116, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447971, "dur": 3, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282447977, "dur": 65, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448049, "dur": 4, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448056, "dur": 34, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448093, "dur": 2, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448097, "dur": 48, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448151, "dur": 3, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448157, "dur": 58, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448217, "dur": 4, "ph": "X", "name": "ProcessMessages 1613", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448223, "dur": 67, "ph": "X", "name": "ReadAsync 1613", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448296, "dur": 4, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448303, "dur": 175, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448484, "dur": 6, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448493, "dur": 98, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448597, "dur": 9, "ph": "X", "name": "ProcessMessages 3737", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448609, "dur": 38, "ph": "X", "name": "ReadAsync 3737", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448650, "dur": 3, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448655, "dur": 61, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448724, "dur": 3, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448730, "dur": 35, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448767, "dur": 3, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448772, "dur": 49, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448827, "dur": 3, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448833, "dur": 41, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448877, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448881, "dur": 54, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448941, "dur": 3, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448947, "dur": 44, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448993, "dur": 3, "ph": "X", "name": "ProcessMessages 1351", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282448998, "dur": 53, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449057, "dur": 3, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449063, "dur": 69, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449138, "dur": 5, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449146, "dur": 43, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449192, "dur": 3, "ph": "X", "name": "ProcessMessages 1593", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449197, "dur": 47, "ph": "X", "name": "ReadAsync 1593", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449250, "dur": 3, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449256, "dur": 69, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449332, "dur": 4, "ph": "X", "name": "ProcessMessages 1268", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449339, "dur": 35, "ph": "X", "name": "ReadAsync 1268", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449377, "dur": 3, "ph": "X", "name": "ProcessMessages 1310", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449382, "dur": 45, "ph": "X", "name": "ReadAsync 1310", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449433, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449440, "dur": 48, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449491, "dur": 3, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449496, "dur": 42, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449544, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449550, "dur": 79, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449636, "dur": 4, "ph": "X", "name": "ProcessMessages 1339", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449643, "dur": 91, "ph": "X", "name": "ReadAsync 1339", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449740, "dur": 5, "ph": "X", "name": "ProcessMessages 1647", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449748, "dur": 52, "ph": "X", "name": "ReadAsync 1647", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449802, "dur": 3, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449808, "dur": 137, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449952, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282449956, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450011, "dur": 5, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450019, "dur": 63, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450088, "dur": 4, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450094, "dur": 93, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450194, "dur": 5, "ph": "X", "name": "ProcessMessages 1876", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450201, "dur": 61, "ph": "X", "name": "ReadAsync 1876", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450269, "dur": 4, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450275, "dur": 67, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450346, "dur": 4, "ph": "X", "name": "ProcessMessages 1629", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450352, "dur": 71, "ph": "X", "name": "ReadAsync 1629", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450431, "dur": 4, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450439, "dur": 66, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450512, "dur": 5, "ph": "X", "name": "ProcessMessages 1763", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450520, "dur": 35, "ph": "X", "name": "ReadAsync 1763", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450557, "dur": 3, "ph": "X", "name": "ProcessMessages 1470", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450562, "dur": 29, "ph": "X", "name": "ReadAsync 1470", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450596, "dur": 3, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450601, "dur": 46, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450651, "dur": 3, "ph": "X", "name": "ProcessMessages 1128", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450656, "dur": 64, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450726, "dur": 4, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450732, "dur": 40, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450775, "dur": 3, "ph": "X", "name": "ProcessMessages 1381", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450780, "dur": 32, "ph": "X", "name": "ReadAsync 1381", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450816, "dur": 3, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450823, "dur": 35, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450862, "dur": 3, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450867, "dur": 54, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450927, "dur": 4, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450933, "dur": 33, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450969, "dur": 3, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282450974, "dur": 25, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451003, "dur": 2, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451007, "dur": 59, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451073, "dur": 4, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451079, "dur": 41, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451122, "dur": 4, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451128, "dur": 35, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451167, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451172, "dur": 33, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451210, "dur": 3, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451215, "dur": 50, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451271, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451277, "dur": 33, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451312, "dur": 3, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451317, "dur": 186, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451508, "dur": 3, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451514, "dur": 77, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451595, "dur": 8, "ph": "X", "name": "ProcessMessages 3728", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451605, "dur": 59, "ph": "X", "name": "ReadAsync 3728", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451671, "dur": 4, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451677, "dur": 49, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451731, "dur": 4, "ph": "X", "name": "ProcessMessages 1337", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451737, "dur": 44, "ph": "X", "name": "ReadAsync 1337", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451785, "dur": 3, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451790, "dur": 61, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451857, "dur": 3, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451863, "dur": 40, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451906, "dur": 3, "ph": "X", "name": "ProcessMessages 1612", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451911, "dur": 33, "ph": "X", "name": "ReadAsync 1612", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451949, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282451953, "dur": 66, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452025, "dur": 4, "ph": "X", "name": "ProcessMessages 1090", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452032, "dur": 37, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452071, "dur": 2, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452076, "dur": 33, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452113, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452119, "dur": 55, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452180, "dur": 3, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452186, "dur": 50, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452238, "dur": 4, "ph": "X", "name": "ProcessMessages 1229", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452244, "dur": 31, "ph": "X", "name": "ReadAsync 1229", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452279, "dur": 3, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452284, "dur": 47, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452335, "dur": 3, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452341, "dur": 52, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452399, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452405, "dur": 46, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452454, "dur": 4, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452459, "dur": 55, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452521, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452527, "dur": 39, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452569, "dur": 3, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452574, "dur": 30, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452608, "dur": 3, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452613, "dur": 44, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452666, "dur": 3, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452672, "dur": 55, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452733, "dur": 4, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452739, "dur": 34, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452775, "dur": 3, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452780, "dur": 30, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452815, "dur": 3, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452820, "dur": 58, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452884, "dur": 4, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452891, "dur": 39, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452932, "dur": 3, "ph": "X", "name": "ProcessMessages 1233", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452938, "dur": 30, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452973, "dur": 3, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282452979, "dur": 30, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453012, "dur": 3, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453017, "dur": 47, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453072, "dur": 4, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453078, "dur": 54, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453136, "dur": 5, "ph": "X", "name": "ProcessMessages 1674", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453143, "dur": 59, "ph": "X", "name": "ReadAsync 1674", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453208, "dur": 4, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453214, "dur": 39, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453256, "dur": 3, "ph": "X", "name": "ProcessMessages 1365", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453261, "dur": 30, "ph": "X", "name": "ReadAsync 1365", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453295, "dur": 3, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453300, "dur": 40, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453345, "dur": 4, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453351, "dur": 55, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453413, "dur": 4, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453419, "dur": 43, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453465, "dur": 3, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453470, "dur": 37, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453513, "dur": 3, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453519, "dur": 32, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453553, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453557, "dur": 23, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453584, "dur": 2, "ph": "X", "name": "ProcessMessages 55", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453588, "dur": 141, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453736, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453741, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453845, "dur": 8, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453855, "dur": 72, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453934, "dur": 7, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282453944, "dur": 55, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454007, "dur": 7, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454017, "dur": 71, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454101, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454111, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454186, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454197, "dur": 81, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454288, "dur": 7, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454299, "dur": 59, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454364, "dur": 3, "ph": "X", "name": "ProcessMessages 39", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454395, "dur": 102, "ph": "X", "name": "ReadAsync 39", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454506, "dur": 19, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454528, "dur": 78, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454614, "dur": 10, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454628, "dur": 85, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454720, "dur": 11, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454734, "dur": 59, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454800, "dur": 7, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454810, "dur": 62, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454883, "dur": 7, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282454894, "dur": 225, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282455128, "dur": 7, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282455138, "dur": 55, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282455201, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282455208, "dur": 2086, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282457329, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282457340, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282457436, "dur": 3359, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282460823, "dur": 432, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282461275, "dur": 18, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282461297, "dur": 694, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462012, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462025, "dur": 256, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462290, "dur": 18, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462311, "dur": 55, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462370, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462376, "dur": 59, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462443, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462448, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462550, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462558, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462689, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462696, "dur": 132, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462835, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282462843, "dur": 140, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463000, "dur": 7, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463011, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463095, "dur": 11, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463108, "dur": 240, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463360, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463365, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463434, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463439, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463498, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463505, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463558, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463564, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463618, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463623, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463699, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463704, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463746, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463751, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463826, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463831, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463890, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463895, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463935, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282463939, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464003, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464044, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464048, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464138, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464143, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464213, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464219, "dur": 74, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464300, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464305, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464345, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464349, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464386, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464389, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464419, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464422, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464709, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464717, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464795, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464801, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464851, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464857, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464882, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464885, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464911, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464914, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464949, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282464953, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465137, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465142, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465194, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465198, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465240, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465245, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465281, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465285, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465347, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465352, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465403, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465407, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465455, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465461, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465513, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465519, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465573, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465578, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465617, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465622, "dur": 186, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465816, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465822, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465852, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465883, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465888, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465976, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282465979, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282466003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282466006, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282466037, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282466041, "dur": 5042, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282471101, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282471112, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282471241, "dur": 12, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282471258, "dur": 234211, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282705484, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282705491, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282705612, "dur": 34, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282705649, "dur": 3878, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282709538, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282709545, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282709619, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 33900, "tid": 21474836480, "ts": 1754039282709625, "dur": 8038, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282735199, "dur": 2408, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 33900, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 33900, "tid": 17179869184, "ts": 1754039282382616, "dur": 11, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 33900, "tid": 17179869184, "ts": 1754039282382628, "dur": 16835, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 33900, "tid": 17179869184, "ts": 1754039282399464, "dur": 63, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282737613, "dur": 26, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 33900, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 33900, "tid": 1, "ts": 1754039281818302, "dur": 6328, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 33900, "tid": 1, "ts": 1754039281824640, "dur": 51896, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 33900, "tid": 1, "ts": 1754039281876552, "dur": 65624, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282737645, "dur": 17, "ph": "X", "name": "", "args": {}}, {"pid": 33900, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281815956, "dur": 7571, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281823531, "dur": 127916, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281824471, "dur": 3985, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281828481, "dur": 2038, "ph": "X", "name": "ProcessMessages 20509", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281830530, "dur": 358, "ph": "X", "name": "ReadAsync 20509", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281830897, "dur": 33, "ph": "X", "name": "ProcessMessages 20493", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281830933, "dur": 73, "ph": "X", "name": "ReadAsync 20493", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831011, "dur": 4, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831017, "dur": 136, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831161, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831224, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831230, "dur": 60, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831303, "dur": 9, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831318, "dur": 74, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831396, "dur": 7, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831408, "dur": 59, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831472, "dur": 6, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831482, "dur": 41, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831528, "dur": 5, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831537, "dur": 51, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831592, "dur": 3, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831599, "dur": 57, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831662, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831668, "dur": 35, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831706, "dur": 3, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831711, "dur": 31, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831748, "dur": 3, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831753, "dur": 59, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831818, "dur": 4, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831825, "dur": 40, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831867, "dur": 3, "ph": "X", "name": "ProcessMessages 1365", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831873, "dur": 26, "ph": "X", "name": "ReadAsync 1365", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831903, "dur": 2, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831908, "dur": 59, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831973, "dur": 3, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281831979, "dur": 37, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832019, "dur": 3, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832024, "dur": 30, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832059, "dur": 3, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832064, "dur": 65, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832136, "dur": 4, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832142, "dur": 35, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832180, "dur": 3, "ph": "X", "name": "ProcessMessages 1175", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832185, "dur": 31, "ph": "X", "name": "ReadAsync 1175", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832220, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832225, "dur": 57, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832288, "dur": 4, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832294, "dur": 35, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832333, "dur": 2, "ph": "X", "name": "ProcessMessages 1111", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832338, "dur": 32, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832374, "dur": 3, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832378, "dur": 55, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832439, "dur": 4, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832446, "dur": 40, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832488, "dur": 3, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832493, "dur": 42, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832539, "dur": 3, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832544, "dur": 53, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832604, "dur": 4, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832611, "dur": 36, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832649, "dur": 3, "ph": "X", "name": "ProcessMessages 1355", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832655, "dur": 29, "ph": "X", "name": "ReadAsync 1355", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832687, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832692, "dur": 53, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832752, "dur": 3, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832757, "dur": 40, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832800, "dur": 3, "ph": "X", "name": "ProcessMessages 1566", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832806, "dur": 28, "ph": "X", "name": "ReadAsync 1566", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832838, "dur": 3, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832843, "dur": 33, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832879, "dur": 2, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832883, "dur": 35, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832924, "dur": 3, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832930, "dur": 53, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832988, "dur": 3, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281832994, "dur": 89, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833089, "dur": 4, "ph": "X", "name": "ProcessMessages 1315", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833096, "dur": 48, "ph": "X", "name": "ReadAsync 1315", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833148, "dur": 3, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833153, "dur": 156, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833315, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833320, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833378, "dur": 4, "ph": "X", "name": "ProcessMessages 1752", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833384, "dur": 76, "ph": "X", "name": "ReadAsync 1752", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833467, "dur": 4, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833474, "dur": 58, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833536, "dur": 5, "ph": "X", "name": "ProcessMessages 2001", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833544, "dur": 84, "ph": "X", "name": "ReadAsync 2001", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833634, "dur": 4, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833640, "dur": 53, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833697, "dur": 4, "ph": "X", "name": "ProcessMessages 1584", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833704, "dur": 82, "ph": "X", "name": "ReadAsync 1584", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833792, "dur": 4, "ph": "X", "name": "ProcessMessages 1305", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833799, "dur": 51, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833854, "dur": 4, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833860, "dur": 66, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833932, "dur": 4, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833938, "dur": 55, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281833998, "dur": 3, "ph": "X", "name": "ProcessMessages 1304", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834003, "dur": 78, "ph": "X", "name": "ReadAsync 1304", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834088, "dur": 5, "ph": "X", "name": "ProcessMessages 1441", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834095, "dur": 36, "ph": "X", "name": "ReadAsync 1441", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834134, "dur": 2, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834138, "dur": 29, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834171, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834176, "dur": 32, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834211, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834215, "dur": 31, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834249, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834253, "dur": 89, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834355, "dur": 9, "ph": "X", "name": "ProcessMessages 1174", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834369, "dur": 90, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834464, "dur": 10, "ph": "X", "name": "ProcessMessages 1983", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834478, "dur": 51, "ph": "X", "name": "ReadAsync 1983", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834534, "dur": 6, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834544, "dur": 48, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834595, "dur": 3, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834601, "dur": 69, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834676, "dur": 4, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834682, "dur": 44, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834730, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834736, "dur": 65, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834807, "dur": 4, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834813, "dur": 42, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834858, "dur": 3, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834863, "dur": 43, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834910, "dur": 3, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834915, "dur": 66, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834988, "dur": 6, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281834996, "dur": 35, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835034, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835038, "dur": 32, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835074, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835078, "dur": 57, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835141, "dur": 4, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835148, "dur": 48, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835200, "dur": 4, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835206, "dur": 66, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835279, "dur": 4, "ph": "X", "name": "ProcessMessages 1128", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835285, "dur": 39, "ph": "X", "name": "ReadAsync 1128", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835327, "dur": 2, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835332, "dur": 27, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835363, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835367, "dur": 57, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835431, "dur": 3, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835437, "dur": 39, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835478, "dur": 3, "ph": "X", "name": "ProcessMessages 1381", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835484, "dur": 39, "ph": "X", "name": "ReadAsync 1381", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835533, "dur": 6, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835542, "dur": 55, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835600, "dur": 3, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835605, "dur": 32, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835641, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835645, "dur": 35, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835682, "dur": 3, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835687, "dur": 42, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835733, "dur": 3, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835738, "dur": 29, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835771, "dur": 3, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835776, "dur": 32, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835810, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835815, "dur": 41, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835859, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835864, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835891, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835895, "dur": 44, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835943, "dur": 3, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835948, "dur": 29, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835980, "dur": 3, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281835985, "dur": 46, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836035, "dur": 3, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836040, "dur": 29, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836072, "dur": 3, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836077, "dur": 34, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836115, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836120, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836158, "dur": 3, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836163, "dur": 44, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836210, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836215, "dur": 29, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836247, "dur": 2, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836251, "dur": 21, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836276, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836279, "dur": 34, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836318, "dur": 2, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836322, "dur": 29, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836354, "dur": 3, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836358, "dur": 45, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836407, "dur": 3, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836412, "dur": 30, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836444, "dur": 2, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836449, "dur": 40, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836493, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836499, "dur": 31, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836533, "dur": 2, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836537, "dur": 31, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836571, "dur": 2, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836575, "dur": 53, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836634, "dur": 4, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836641, "dur": 46, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836689, "dur": 4, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836695, "dur": 34, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836734, "dur": 3, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836739, "dur": 67, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836812, "dur": 4, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836820, "dur": 46, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836868, "dur": 3, "ph": "X", "name": "ProcessMessages 1365", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836874, "dur": 35, "ph": "X", "name": "ReadAsync 1365", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836913, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836917, "dur": 51, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836975, "dur": 4, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281836981, "dur": 52, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837036, "dur": 3, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837041, "dur": 32, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837077, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837082, "dur": 48, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837136, "dur": 3, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837142, "dur": 33, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837178, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837181, "dur": 143, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837332, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837337, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837415, "dur": 555, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281837984, "dur": 154, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281838148, "dur": 22, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281838212, "dur": 136, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281838359, "dur": 355, "ph": "X", "name": "ProcessMessages 1353", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281838724, "dur": 165, "ph": "X", "name": "ReadAsync 1353", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281838901, "dur": 25, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281838930, "dur": 3560, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842506, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842516, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842559, "dur": 5, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842567, "dur": 216, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842793, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842799, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842884, "dur": 7, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842895, "dur": 86, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842990, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281842996, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281843555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281843560, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281843628, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281843634, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281843700, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281843704, "dur": 375, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844098, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844108, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844204, "dur": 6, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844213, "dur": 71, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844292, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844297, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844330, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844336, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844388, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844394, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844426, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844431, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844544, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844549, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844580, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281844585, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845146, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845157, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845226, "dur": 9, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845238, "dur": 29, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845271, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845276, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845318, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845324, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845374, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845381, "dur": 82, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845479, "dur": 7, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845490, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845545, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845549, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845729, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845735, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845793, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281845799, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846021, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846027, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846116, "dur": 7, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846126, "dur": 56, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846190, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846197, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846256, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846262, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846318, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846323, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846351, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846355, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846397, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846402, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846473, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846478, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846507, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281846511, "dur": 699, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847223, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847230, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847291, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847296, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847361, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847407, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847414, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847473, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847539, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847544, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847589, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847593, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847649, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847683, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281847686, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281852507, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281852517, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281852618, "dur": 35, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281852658, "dur": 92677, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281945359, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281945369, "dur": 1155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281946547, "dur": 457, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 33900, "tid": 12884901888, "ts": 1754039281947017, "dur": 4337, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282737667, "dur": 1669, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 33900, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 33900, "tid": 8589934592, "ts": 1754039281812995, "dur": 129246, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 33900, "tid": 8589934592, "ts": 1754039281942245, "dur": 12, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 33900, "tid": 8589934592, "ts": 1754039281942259, "dur": 1856, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282739343, "dur": 24, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 33900, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039281789920, "dur": 163051, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039281796158, "dur": 7965, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039281953229, "dur": 420215, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039282373640, "dur": 344089, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039282373807, "dur": 8744, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039282718163, "dur": 5335, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039282721084, "dur": 117, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 33900, "tid": 4294967296, "ts": 1754039282723510, "dur": 24, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282739372, "dur": 44, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754039282400623, "dur": 44116, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282444750, "dur": 267, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282445068, "dur": 55, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754039282445123, "dur": 876, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282446324, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_2882AD42843F283E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039282446675, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9755A17737AC9F6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039282446883, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6374C97D56DC6B03.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039282447339, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_85536166D0FBCC00.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039282448012, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_62BDE33F3F4A02C5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039282449722, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039282452746, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754039282446026, "dur": 8746, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282454781, "dur": 253023, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282707806, "dur": 464, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282708270, "dur": 52, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282710609, "dur": 64, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282710691, "dur": 1204, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754039282446365, "dur": 8430, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282454800, "dur": 1185, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282456116, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754039282456178, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282456582, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282456803, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282457194, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\GraphInputAnalyser.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754039282457017, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282457846, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282458119, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282458403, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Events\\EventHooks.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754039282458374, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282459211, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282459476, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282459776, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282460062, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282460164, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282460261, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282460859, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282461295, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039282461462, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039282461989, "dur": 812, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282462821, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282463076, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282463221, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754039282463423, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282463800, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282463862, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282463982, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282464167, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282464598, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282464720, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282465108, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282465362, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282465417, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282465979, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282466394, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282466583, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282466695, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282466751, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039282467044, "dur": 240940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282446626, "dur": 8234, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282454995, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6374C97D56DC6B03.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039282455282, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_928D6AC700018D76.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039282455389, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_928D6AC700018D76.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039282455658, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754039282455860, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754039282456058, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754039282456134, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282456382, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282456799, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282457037, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282457602, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282457927, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282458258, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282458954, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282459236, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282459548, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282459813, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282460162, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282460279, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282460869, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282461303, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039282461443, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754039282462690, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282462776, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282463286, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039282463503, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754039282464445, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282464617, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282464710, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282464785, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282465069, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282465361, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282465441, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282465916, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282465983, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282466369, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282466593, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282466693, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282466754, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039282467052, "dur": 240734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282446465, "dur": 8338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282454812, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DC8070BE7215D7F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039282454978, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282455187, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_6A2D7917AFB992E0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039282455312, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F91DBC4BC6C5A3E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039282455412, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F91DBC4BC6C5A3E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039282455647, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754039282455884, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754039282456034, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754039282456163, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282456548, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282456801, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282457044, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282457312, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282457540, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282457836, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282458082, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282458338, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282459079, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282459304, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282459587, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282459846, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282460166, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282460272, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282460866, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282461263, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282461325, "dur": 1674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282463058, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282463582, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282463901, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282463995, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282464134, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282464585, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282464650, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282464715, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282465077, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282465419, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282465976, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282466383, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282466584, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282466688, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282466750, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039282467079, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039282467218, "dur": 240627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282446519, "dur": 8294, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282455065, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_1C3E8F9E6ECB0F0D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039282455281, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37BB1ECA717C551A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039282455388, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37BB1ECA717C551A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039282455651, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754039282455760, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754039282455891, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754039282456126, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282456571, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282456783, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282457162, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282457431, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282457708, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282458065, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282458320, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282459052, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282459317, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282459977, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282460178, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282460284, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282460860, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282461315, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039282461579, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754039282462025, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282462131, "dur": 722, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Subsystem.Registration.ref.dll_E45021852246D57F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039282463052, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282463167, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282463234, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282463800, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282463894, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282463969, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282464144, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282464601, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282464740, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282465079, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282465470, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282465912, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282465995, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282466385, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282466594, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282466699, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282466756, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039282467045, "dur": 240920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282446603, "dur": 8222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282454831, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1647E0E435870013.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282455114, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_85536166D0FBCC00.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282455269, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282455362, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_71A46DF186FCA375.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282455579, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754039282455776, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754039282456053, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754039282456144, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282456686, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282456955, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282457208, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282457459, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282457706, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282458016, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282458362, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282459118, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282459547, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282459807, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282460179, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282460277, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282460882, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282461250, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282461307, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282461465, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039282462773, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282463073, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282463232, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282463351, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282463719, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039282465086, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282465204, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039282465991, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282466129, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039282466694, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282466783, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039282467072, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039282467417, "dur": 4122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754039282467173, "dur": 4370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039282472104, "dur": 56, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039282472174, "dur": 234452, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754039282446681, "dur": 8203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282455100, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_573D391D20F53B3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039282455267, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A715D5493BAA0805.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039282455425, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754039282455636, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754039282455986, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754039282456042, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754039282456127, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282456554, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282456788, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282457069, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282457338, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282457603, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282457907, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282458159, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282458459, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282459151, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282459455, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282459688, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282459972, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282460173, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282460269, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282460874, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282461256, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282461309, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039282461509, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754039282462230, "dur": 677, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754039282463001, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282463073, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282463246, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282463841, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282463998, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282464177, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282464589, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282464718, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282465066, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282465366, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282465423, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282465917, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282465996, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282466370, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282466610, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282466684, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282466753, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039282467047, "dur": 240844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282446740, "dur": 8155, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282455112, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_69B2D04496769853.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039282455301, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.OpenHarmony.Types.dll_D2F195706FD3294C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039282455443, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282455601, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754039282455776, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754039282456118, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282456364, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282456964, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282457289, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282457537, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282457786, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282458038, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282458293, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282459359, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282459712, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282459954, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282460266, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282460882, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282461249, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282461313, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039282461450, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282461832, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754039282462383, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282462792, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mobile.AndroidLogcat.Editor.ref.dll_7371635DA3372C15.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039282463023, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754039282463220, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282463305, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039282463486, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754039282463869, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282463990, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282464136, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282464592, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282464714, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282465067, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282465430, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282465981, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282466373, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282466586, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282466701, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282466758, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039282467054, "dur": 240878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282446802, "dur": 8116, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282454973, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282455111, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_3BDE6172FEF683CE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039282455325, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_A5B7D5FE55900AD0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039282455678, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754039282455874, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Unified.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754039282456113, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754039282456172, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282456645, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282457018, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282457273, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282457486, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282457736, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282458099, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282458335, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282459086, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282459348, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282459975, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282460311, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282460867, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282461303, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039282461413, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754039282462263, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282462755, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039282463005, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754039282463680, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282463835, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282463988, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282464133, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282464636, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282464716, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282465064, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282465363, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282465424, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282465922, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282465975, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282466360, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282466580, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282466682, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282466749, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039282467046, "dur": 240744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282446849, "dur": 8098, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282454954, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_DBC6D836FFBB2344.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039282455279, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_8B40DE9AA68ADB6B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039282455410, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754039282455653, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754039282455852, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754039282456020, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754039282456113, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282456602, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282457212, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282457452, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282457660, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282457912, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282458191, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282458448, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282459143, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282459495, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282459796, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282460046, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282460105, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282460165, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282460259, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282460870, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282461315, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039282461449, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282461667, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754039282462707, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282462778, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282463063, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282463160, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282463240, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282463861, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282463987, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282464126, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282464618, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282464738, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282465074, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282465370, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282465449, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282465925, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282465976, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282466361, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282466582, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282466681, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282466752, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039282467066, "dur": 240740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282446896, "dur": 8069, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282454972, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A2E81F1AD09828BF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282455110, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A955AE47006AFF49.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282455294, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6A79E1CEFAB6A278.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282455525, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754039282455679, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754039282455882, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754039282456166, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282456568, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282456962, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282457308, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282457556, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282457845, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282458085, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282458346, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282459048, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282459517, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282459830, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282460271, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282460857, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282461245, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282461297, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282461433, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754039282462617, "dur": 464, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754039282463161, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282463222, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Notifications.iOS.ref.dll_D62165F1A8340576.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282463284, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282463585, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754039282464152, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282464599, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282464737, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039282464894, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754039282465422, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282465980, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282466376, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282466597, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282466750, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039282467064, "dur": 240811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282446932, "dur": 8051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282454991, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4BC2AA918D52165E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039282455093, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4BC2AA918D52165E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039282455265, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_A5F903777A55BB64.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039282455656, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754039282455892, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754039282456117, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282456356, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282456800, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282457093, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282457318, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282457538, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282458175, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282458463, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282459322, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282459588, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282459832, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282460165, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282460257, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282460863, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282461319, "dur": 1648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282462973, "dur": 745, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282463728, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282463892, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282463976, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282464127, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282464591, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282464739, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282465076, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282465419, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282466000, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282466378, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282466591, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282466705, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282466759, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039282467063, "dur": 240759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282446980, "dur": 8016, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282455104, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_6EC10AF50D2A6196.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039282455256, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C53307B6A7BEB060.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039282455853, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754039282455985, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754039282456184, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282456694, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282456964, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282457211, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282457509, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282457751, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282458121, "dur": 820, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SwitchOnEnum.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754039282458032, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282459101, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282459407, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282459717, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282460095, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282460163, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282460260, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282460858, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282461242, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282461301, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039282461430, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282461516, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754039282462010, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282462105, "dur": 550, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754039282462711, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282462815, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282462981, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282463440, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282463802, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282463877, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282463999, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282464148, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282464595, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282464737, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282465095, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282465414, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282465467, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039282465598, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754039282465992, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039282466117, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754039282466596, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282466701, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282466759, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039282467055, "dur": 240848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282447036, "dur": 7967, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282455066, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_16A086F828650CC5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039282455271, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282455462, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039282455637, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039282455764, "dur": 3772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754039282459641, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282459714, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282459989, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282460163, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282460262, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282460861, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282461291, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039282461426, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754039282462762, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282463010, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754039282463234, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754039282463683, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282463799, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282463977, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282464142, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282464582, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282464713, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282465093, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282465427, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282466002, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282466363, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282466599, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282466689, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282466771, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039282467049, "dur": 240805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039282447136, "dur": 7899, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039282455104, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_48EC27BC93FA8CF8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282455264, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_62BDE33F3F4A02C5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282455461, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754039282455591, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282455706, "dur": 4488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282460290, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282460371, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282460887, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282460956, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282461291, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282461435, "dur": 1616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282463230, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282463343, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282464605, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039282464735, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282464896, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282465462, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282465576, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282465994, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282466072, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282466375, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039282466458, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039282466774, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039282467058, "dur": 240940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282447206, "dur": 7839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282455099, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_F9AFAB8C871FA504.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039282455272, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282455359, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_43E71E211D55C301.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039282455556, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754039282455650, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754039282455778, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754039282456157, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282456395, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282456779, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282457036, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282457323, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282457607, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282457993, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282458249, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282458932, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282459251, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282460063, "dur": 78, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282460164, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282460276, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282460864, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282461259, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282461318, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039282461468, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039282462218, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282462445, "dur": 546, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039282463001, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282463165, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282463241, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282463848, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282463997, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282464131, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282464585, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282464774, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282465072, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282465364, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282465415, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282465983, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282466367, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282466578, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282466683, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282466767, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039282467059, "dur": 240884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282447262, "dur": 7817, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282455188, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B052D4C0DDBC5E9B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754039282455336, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_14BC28C643E9DDDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754039282455471, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754039282455544, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754039282455657, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754039282455882, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754039282457266, "dur": 373, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754039282457642, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282457927, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282458209, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282458925, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282459229, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282459819, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282460278, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282460871, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282461332, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282462176, "dur": 721, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Subsystem.Registration.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754039282462985, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754039282463066, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282463232, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754039282463457, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282463868, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282463980, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282464139, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282464587, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282464712, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282465070, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282465418, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282465907, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282465977, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282466362, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282466579, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282466708, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282466769, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039282467056, "dur": 240781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039282716565, "dur": 1752, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754039282023248, "dur": 330447, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754039282024174, "dur": 55111, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754039282320361, "dur": 3848, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754039282324213, "dur": 29470, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754039282325396, "dur": 24539, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754039282360356, "dur": 1395, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754039282359490, "dur": 2660, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754039281822523, "dur": 1405, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281823943, "dur": 347, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281824375, "dur": 486, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281825773, "dur": 3037, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_3192D1363CD74828.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039281830304, "dur": 2730, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754039281833576, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754039281836549, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754039281824892, "dur": 14377, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281839279, "dur": 107347, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281946628, "dur": 324, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281946952, "dur": 61, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281947016, "dur": 54, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281947080, "dur": 59, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281947145, "dur": 55, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281947300, "dur": 63, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281947395, "dur": 1519, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754039281825103, "dur": 14242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281839357, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1647E0E435870013.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281839584, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_85C5F6B134E08640.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281839780, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_C93B6CDD0DD38F2A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281839841, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281839991, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754039281840136, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754039281840208, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754039281840570, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754039281840659, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754039281840779, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281841065, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281841641, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\ExposeOption.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754039281841618, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281842424, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281842730, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281842990, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281843232, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281843859, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281844392, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281844823, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281844902, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281845657, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281846535, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281847358, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281847584, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281848287, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754039281848374, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281848582, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281849662, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281849783, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281850407, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281850541, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281851010, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281851115, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281851466, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754039281851566, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281851857, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754039281852112, "dur": 94562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281825042, "dur": 14262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281839316, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DC8070BE7215D7F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039281839799, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_A5B7D5FE55900AD0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039281840103, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754039281840202, "dur": 256, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754039281840577, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754039281840649, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754039281840753, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281841403, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281841666, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281842039, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281842297, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281842651, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281843040, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281843659, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281843899, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281844212, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281844281, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281844763, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281844900, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281845627, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281846214, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281846274, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754039281846419, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281846496, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754039281847057, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281847228, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281847370, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281847468, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281847979, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281848097, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281848179, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281848235, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281848332, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281848428, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281848578, "dur": 792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281849371, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281849669, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281849906, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281850389, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281850535, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281850990, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281851431, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281851858, "dur": 94370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754039281946231, "dur": 331, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281825092, "dur": 14232, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281839498, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7CFDE3E5ED4F90EA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039281839790, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.OpenHarmony.Types.dll_D2F195706FD3294C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039281840016, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754039281840172, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754039281840408, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754039281840749, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281841228, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281841456, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281841678, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281842025, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281842284, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281842795, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281843049, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281843641, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281843897, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281844127, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281844369, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281844809, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281844903, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281845626, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281846212, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281846302, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039281846442, "dur": 1440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754039281847884, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281848007, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754039281848186, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754039281848637, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281849372, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281849675, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281849895, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281850399, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281850562, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281850916, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281851007, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281851438, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754039281851881, "dur": 94803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281825280, "dur": 14111, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281839405, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_69DEA09F154B1449.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039281839511, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281839615, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_85536166D0FBCC00.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039281839794, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281839922, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039281840044, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754039281840200, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754039281840638, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754039281840759, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281841005, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281841514, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281841762, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281842256, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281842538, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281842922, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281843236, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281844043, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.1\\Editor\\PlasticSCM\\Views\\PendingChanges\\UnityPendingChangesTree.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754039281843854, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281844765, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281844898, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281845621, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281846204, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281846256, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039281846427, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754039281847101, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281847351, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754039281847564, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754039281848244, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281848337, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281848411, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281848574, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281849388, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281849663, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281849888, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281850386, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281850531, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281850919, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281850970, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281851033, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281851401, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281851452, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754039281851871, "dur": 94759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281825135, "dur": 14225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281839365, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_8581FBE20058516F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039281839509, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1EDD99B65AC3A75C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039281839737, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_4F4F3C41F5793FA4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039281839833, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281840069, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754039281840182, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754039281840410, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754039281840754, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281840987, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281841448, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281841771, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281842098, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281842360, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281842808, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281843225, "dur": 882, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionStay2DMessageListener.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754039281843080, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281844280, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281844389, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281844607, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281844770, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281844937, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281845646, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281846346, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039281846539, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039281847261, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281847415, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mobile.AndroidLogcat.Editor.ref.dll_7371635DA3372C15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039281847517, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281847963, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281848160, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281848251, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281848334, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281848420, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281848583, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281849375, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281849678, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281849908, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281850409, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754039281850496, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281850559, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754039281850925, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281850995, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281851457, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754039281851866, "dur": 94803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281825174, "dur": 14198, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281839519, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_E9755A17737AC9F6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039281839769, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_98E9B9142E991CAE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039281839917, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039281840043, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754039281840191, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Android.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754039281840432, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754039281840617, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.Unified.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754039281840760, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281841278, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281841558, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281841822, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281842712, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281843235, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281843858, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281844145, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281844410, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281844771, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281844906, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281845633, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281846525, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754039281847404, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281847485, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281847968, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281848092, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281848160, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281848220, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281848325, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281848376, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281848591, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281849386, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281849667, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281849907, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281850387, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281850545, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281850917, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281851004, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281851470, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754039281851581, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754039281851863, "dur": 94807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281825225, "dur": 14157, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281839629, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A955AE47006AFF49.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039281839736, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B052D4C0DDBC5E9B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039281839808, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2E00064E3669CC14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754039281840024, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754039281840221, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754039281840541, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754039281840642, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754039281840774, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281841055, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281841440, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281841675, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281842018, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281842260, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281842603, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281842896, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281843202, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281843768, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281844011, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281844515, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281844841, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281844920, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281845651, "dur": 874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281846526, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281847275, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281847431, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281847517, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281847967, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281848089, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281848157, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281848226, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281848380, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281848576, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281849370, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281849643, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281849886, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281850382, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281850536, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281850911, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281851011, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281851439, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754039281851878, "dur": 94745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281825306, "dur": 14121, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281839790, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6A79E1CEFAB6A278.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039281839994, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754039281840209, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754039281840745, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281841245, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281841511, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281841813, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281842342, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281842662, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281842929, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281843275, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281843862, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281844148, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281844393, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281844674, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281844933, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281845636, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281846252, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039281846439, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754039281847263, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281847353, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Subsystem.Registration.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754039281847457, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281847817, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281847969, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281848095, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281848158, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281848222, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281848402, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281848577, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281849395, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754039281849506, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754039281849906, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281850391, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281850563, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281850905, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281851040, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281851451, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754039281851883, "dur": 94789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281825348, "dur": 14105, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281839786, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281839901, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039281840003, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039281840308, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039281840451, "dur": 4028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754039281844606, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281844969, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281845661, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281846406, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754039281846611, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281846679, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754039281847380, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754039281847446, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281847527, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281847993, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281848088, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281848227, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281848325, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281848379, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281848571, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281849363, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281849646, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281849889, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281850410, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281850573, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281850898, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281850993, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281851432, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281851857, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754039281852121, "dur": 94573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281825385, "dur": 14076, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281839796, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F91DBC4BC6C5A3E2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039281840061, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754039281840138, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754039281840227, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754039281840625, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754039281840764, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281841256, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281841542, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281841883, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281842187, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281842487, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281842759, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281843081, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281843665, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281843952, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281844366, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281845035, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281845622, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281846273, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754039281846414, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281846536, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754039281847101, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281847184, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281847356, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1754039281847508, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281847976, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281848113, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281848175, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281848232, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281848409, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281848575, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281849409, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281849647, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281849891, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281850384, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281850550, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281850903, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281850994, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281851383, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281851435, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754039281851884, "dur": 94813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281825456, "dur": 14020, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281839485, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_DBC6D836FFBB2344.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039281839625, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FE46A1EBC882AC6C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039281839787, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_928D6AC700018D76.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039281839952, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281840195, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754039281840406, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754039281840670, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754039281840770, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281841247, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281841476, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281841741, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281842023, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281842285, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281842537, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281842801, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281843064, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281843712, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281844099, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281844411, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281844789, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281844901, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281845650, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281846263, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039281846412, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281846533, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754039281847113, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281847266, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754039281847506, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754039281848177, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281848229, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281848398, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281848589, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281849398, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281849658, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281849887, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281850383, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281850553, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281850905, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281850991, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281851445, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754039281851875, "dur": 94773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281825516, "dur": 14003, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281839527, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4BC2AA918D52165E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039281839813, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OpenHarmony.Extensions.dll_FEC45A16915BBBD0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039281839942, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_99B245CEE7EA52CF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039281840180, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754039281840399, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754039281840740, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281841358, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281841765, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281842110, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281842372, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281842639, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281842889, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281843211, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281843771, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281844061, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281844321, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281844773, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281844905, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281845647, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281846327, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039281846508, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754039281846653, "dur": 856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281847516, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754039281848000, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281848104, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281848165, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281848247, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281848377, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281848573, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281849365, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281849678, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281849920, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281850379, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281850527, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281850896, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281851017, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281851443, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754039281851873, "dur": 94785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281825554, "dur": 13975, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281839538, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6374C97D56DC6B03.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039281839695, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281839801, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_14BC28C643E9DDDE.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039281839976, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281840100, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754039281840192, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754039281840394, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754039281840640, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mobile.AndroidLogcat.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754039281840744, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281841259, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281841601, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281841934, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281842350, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281842688, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281843104, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281843684, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281843903, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281844183, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281844303, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281844605, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281844814, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281844907, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281845664, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281846254, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754039281846434, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Subsystem.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754039281846983, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281847330, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281847469, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848014, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848092, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848159, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848226, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848324, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848391, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281848640, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281849366, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281849645, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281849909, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281850380, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281850529, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281850896, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281850991, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281851437, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754039281851860, "dur": 94792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281825597, "dur": 13949, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281839651, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_619915005AD9BF77.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039281839785, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_37BB1ECA717C551A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039281839947, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_F83491EED249B619.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039281840096, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754039281840398, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754039281840782, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281841079, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281841494, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281841756, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281842096, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281842379, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281842630, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281842925, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281843618, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281843875, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281844144, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281844384, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281844646, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281844779, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281844896, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281845624, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281846276, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039281846430, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039281847150, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281847271, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039281847520, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039281848420, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281848597, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281849407, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281849653, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281849904, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754039281850044, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754039281850557, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281850912, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281851026, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281851441, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754039281851859, "dur": 94774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039281825647, "dur": 13905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039281839557, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_2F52DBC9C180386A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281839629, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_2F52DBC9C180386A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281839788, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039281840011, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281840266, "dur": 4547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281844950, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281845059, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281845643, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281845748, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281846251, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281846423, "dur": 1812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281848237, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039281848347, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281848451, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281849394, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281849500, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281849903, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281850037, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281850538, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281850637, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281850989, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754039281851086, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754039281851450, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754039281851862, "dur": 94766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281825686, "dur": 13939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281839783, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_8B40DE9AA68ADB6B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754039281839921, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754039281840177, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Notifications.iOS.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754039281840399, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754039281840623, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754039281840796, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281841233, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281841517, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281841784, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281842280, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281842530, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281842834, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281843268, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281843857, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281844153, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281844508, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281844964, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281845644, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281846528, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281847347, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754039281847424, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281847536, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281847971, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281848090, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281848157, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281848222, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281848404, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281848572, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281849368, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281849644, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281849895, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281850381, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281850527, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281850897, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281851012, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281851394, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281851449, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754039281851880, "dur": 94793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754039281951958, "dur": 1128, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 33900, "tid": 71, "ts": 1754039282740268, "dur": 3276, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 33900, "tid": 71, "ts": 1754039282747614, "dur": 88, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 33900, "tid": 71, "ts": 1754039282747939, "dur": 33, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 33900, "tid": 71, "ts": 1754039282744145, "dur": 3459, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282747772, "dur": 166, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282748007, "dur": 721, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 33900, "tid": 71, "ts": 1754039282731087, "dur": 18878, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}